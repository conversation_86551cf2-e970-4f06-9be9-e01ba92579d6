<!DOCTYPE html>
<html lang="hu">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=yes, maximum-scale=5.0">
    <meta name="description" content="Dolgozói Információs Portál - Hírek, programok és információk">
    <meta name="theme-color" content="#2d2d2d">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="black-translucent">
    <meta name="apple-mobile-web-app-title" content="Info Portál">
    <title>{% block title %}Dolgozói Információs Portál{% endblock %}</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(rgba(44, 44, 44, 0.85), rgba(44, 44, 44, 0.85)),
                        url('/static/images/background.png');
            background-size: cover;
            background-position: center;
            background-attachment: fixed;
            color: #ffffff;
            min-height: 100vh;
            padding-top: 60px;  /* A menü magasságának megfelelő padding */
        }

        /* Mobile optimizations */
        @media (max-width: 768px) {
            body {
                background-attachment: scroll; /* Better performance on mobile */
                padding-top: 70px; /* Adjust for mobile navbar */
            }
        }
        
        .navbar {
            background-color: rgba(45, 45, 45, 0.9) !important;
            backdrop-filter: blur(10px);
        }
        
        .navbar-nav {
            margin-left: auto;
        }
        
        .nav-link {
            color: #ffffff !important;
            margin: 0 10px;
            transition: color 0.3s;
        }
        
        .nav-link:hover {
            color: #007bff !important;
        }
        
        .container-fluid {
            min-height: 100vh;
        }
        
        .content-wrapper {
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: calc(100vh - 80px);
            padding: 20px;
        }
        .dropdown-menu {
            background-color: rgba(45, 45, 45, 0.9);
            backdrop-filter: blur(10px);
        }
        
        .dropdown-item {
            color: #ffffff !important;
            transition: color 0.3s;
        }
        
        .dropdown-item:hover {
            background-color: rgba(0, 123, 255, 0.2);
            color: #007bff !important;
        }

        /* Enhanced mobile navigation */
        @media (max-width: 768px) {
            .navbar-nav {
                margin-left: 0;
                text-align: center;
                padding: 10px 0;
            }

            .nav-link {
                padding: 12px 20px !important;
                margin: 5px 0;
                border-radius: 8px;
                background-color: rgba(255, 255, 255, 0.1);
                transition: all 0.3s ease;
            }

            .nav-link:hover {
                background-color: rgba(0, 123, 255, 0.3);
                transform: translateX(5px);
            }

            .dropdown-menu {
                border: none;
                box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
            }

            .content-wrapper {
                padding: 15px;
                min-height: calc(100vh - 90px);
            }

            /* Touch-friendly buttons */
            .btn {
                min-height: 44px;
                padding: 12px 20px;
                font-size: 16px; /* Prevents zoom on iOS */
            }
        }

        /* Extra small devices */
        @media (max-width: 576px) {
            .content-wrapper {
                padding: 10px;
            }

            .container {
                padding-left: 10px;
                padding-right: 10px;
            }
        }
    </style>
</head>
<!-- A többi rész változatlan -->
<body>
    <nav class="navbar navbar-expand-lg fixed-top">
        <div class="container">
            <a class="navbar-brand text-white" href="{% url 'home' %}">Információs Portál</a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav">
                    <li class="nav-item">
                        <a class="nav-link" href="{% url 'home' %}">Főoldal</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{% url 'announcements' %}">Információk</a>
                    </li>
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                            Alkalmassági vizsgálat
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="https://foglalas.arrabona-med.hu" target="_blank">Időpont foglalás</a></li>
                        </ul>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{% url 'send_email' %}">Email küldése</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{% url 'admin:index' %}">Admin</a>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <div class="container-fluid">
        <div class="content-wrapper">
            {% block content %}
            {% endblock %}
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    <script>
        // Mobile optimizations
        document.addEventListener('DOMContentLoaded', function() {
            // Auto-close mobile menu when clicking on a link
            const navLinks = document.querySelectorAll('.navbar-nav .nav-link');
            const navbarCollapse = document.querySelector('.navbar-collapse');

            navLinks.forEach(link => {
                link.addEventListener('click', () => {
                    if (window.innerWidth <= 768 && navbarCollapse.classList.contains('show')) {
                        const bsCollapse = new bootstrap.Collapse(navbarCollapse);
                        bsCollapse.hide();
                    }
                });
            });

            // Prevent zoom on double tap for iOS
            let lastTouchEnd = 0;
            document.addEventListener('touchend', function (event) {
                const now = (new Date()).getTime();
                if (now - lastTouchEnd <= 300) {
                    event.preventDefault();
                }
                lastTouchEnd = now;
            }, false);

            // Add smooth scrolling for anchor links
            document.querySelectorAll('a[href^="#"]').forEach(anchor => {
                anchor.addEventListener('click', function (e) {
                    e.preventDefault();
                    const target = document.querySelector(this.getAttribute('href'));
                    if (target) {
                        target.scrollIntoView({
                            behavior: 'smooth',
                            block: 'start'
                        });
                    }
                });
            });
        });
    </script>
</body>
</html>