<!DOCTYPE html>
<html lang="hu">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}Dolgozói Információs Portál{% endblock %}</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(rgba(44, 44, 44, 0.85), rgba(44, 44, 44, 0.85)),
                        url('/static/images/background.png');
            background-size: cover;
            background-position: center;
            background-attachment: fixed;
            color: #ffffff;
            min-height: 100vh;
            padding-top: 60px;  /* A <PERSON><PERSON> ma<PERSON><PERSON> meg<PERSON><PERSON><PERSON> padding */
        }
        
        .navbar {
            background-color: rgba(45, 45, 45, 0.9) !important;
            backdrop-filter: blur(10px);
        }
        
        .navbar-nav {
            margin-left: auto;
        }
        
        .nav-link {
            color: #ffffff !important;
            margin: 0 10px;
            transition: color 0.3s;
        }
        
        .nav-link:hover {
            color: #007bff !important;
        }
        
        .container-fluid {
            min-height: 100vh;
        }
        
        .content-wrapper {
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: calc(100vh - 80px);
            padding: 20px;
        }
        .dropdown-menu {
            background-color: rgba(45, 45, 45, 0.9);
            backdrop-filter: blur(10px);
        }
        
        .dropdown-item {
            color: #ffffff !important;
            transition: color 0.3s;
        }
        
        .dropdown-item:hover {
            background-color: rgba(0, 123, 255, 0.2);
            color: #007bff !important;
        }
    </style>
</head>
<!-- A többi rész változatlan -->
<body>
    <nav class="navbar navbar-expand-lg fixed-top">
        <div class="container">
            <a class="navbar-brand text-white" href="{% url 'home' %}">Információs Portál</a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav">
                    <li class="nav-item">
                        <a class="nav-link" href="{% url 'home' %}">Főoldal</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{% url 'announcements' %}">Információk</a>
                    </li>
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                            Alkalmassági vizsgálat
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="https://foglalas.arrabona-med.hu" target="_blank">Időpont foglalás</a></li>
                        </ul>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{% url 'send_email' %}">Email küldése</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{% url 'admin:index' %}">Admin</a>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <div class="container-fluid">
        <div class="content-wrapper">
            {% block content %}
            {% endblock %}
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>