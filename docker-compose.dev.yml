version: '3.8'

services:
  # Django Web Application (Development)
  web:
    build: .
    command: python manage.py runserver 0.0.0.0:8000
    volumes:
      - ./info:/app/info
      - media_volume:/app/media
    ports:
      - "8000:8000"
    environment:
      - DEBUG=True
      - SECRET_KEY=dev-secret-key-not-for-production
      - ALLOWED_HOSTS=localhost,127.0.0.1,0.0.0.0
    restart: unless-stopped

volumes:
  media_volume:
