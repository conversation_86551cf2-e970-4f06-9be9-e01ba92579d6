{% extends 'info_board/base.html' %}

{% block title %}K<PERSON>zlemények{% endblock %}

{% block content %}
<div class="container">
    <h1 class="mb-4">Információk, programok</h1>
    <div class="row row-cols-1 row-cols-md-3 g-4">
        {% for announcement in announcements %}
            <div class="col">
                <div class="card h-100" style="cursor: pointer;" data-bs-toggle="modal" data-bs-target="#modal{{ announcement.id }}">
                    {% if announcement.background_image %}
                        <img src="{{ announcement.background_image.url }}" class="card-img-top" alt="{{ announcement.title }}" style="height: 200px; object-fit: cover;">
                    {% else %}
                        <div class="card-img-top bg-secondary" style="height: 200px;"></div>
                    {% endif %}
                    <div class="card-body">
                        <h5 class="card-title">{{ announcement.title }}</h5>
                    </div>
                </div>
            </div>

            <!-- Modal -->
            <div class="modal fade" id="modal{{ announcement.id }}" tabindex="-1" aria-labelledby="modalLabel{{ announcement.id }}" aria-hidden="true">
                <div class="modal-dialog modal-lg">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title" id="modalLabel{{ announcement.id }}">{{ announcement.title }}</h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                        </div>
                        <div class="modal-body">
                            {% if announcement.background_image %}
                                <img src="{{ announcement.background_image.url }}" class="img-fluid mb-3" alt="{{ announcement.title }}">
                            {% endif %}
                            <p class="text-muted">Közzétéve: {{ announcement.date_posted|date:"Y. F j." }}</p>
                            <div class="content" style="word-wrap: break-word; white-space: pre-wrap; overflow-wrap: break-word;">
                                {{ announcement.content|linebreaks }}
                            </div>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Bezárás</button>
                        </div>
                    </div>
                </div>
            </div>
        {% empty %}
            <div class="col">
                <p>Jelenleg nincsenek elérhető információk.</p>
            </div>
        {% endfor %}
    </div>
</div>

<style>
    .card:hover {
        transform: translateY(-5px);
        transition: transform 0.2s ease-in-out;
        box-shadow: 0 4px 8px rgba(0,0,0,0.1);
    }

    .modal-content{

        color: rgb(0,0,0);
    }

    .content{
        text-wrap:wrap;
    }
</style>
{% endblock %}
