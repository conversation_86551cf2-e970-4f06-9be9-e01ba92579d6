@echo off
REM Create deployment package for InfoTable application

echo ========================================
echo InfoTable Deployment Package Creator
echo ========================================

set PACKAGE_NAME=InfoTable-Deployment
set PACKAGE_DIR=%~dp0%PACKAGE_NAME%

REM Create package directory
if exist "%PACKAGE_DIR%" rmdir /s /q "%PACKAGE_DIR%"
mkdir "%PACKAGE_DIR%"

echo Creating deployment package in: %PACKAGE_DIR%

REM Copy essential files
echo Copying application files...
copy "docker-compose.prod.yml" "%PACKAGE_DIR%\"
copy "Dockerfile" "%PACKAGE_DIR%\"
copy "entrypoint.sh" "%PACKAGE_DIR%\"
copy "nginx.conf" "%PACKAGE_DIR%\"
copy "requirements.txt" "%PACKAGE_DIR%\"
copy "install-service.bat" "%PACKAGE_DIR%\"
copy "uninstall-service.bat" "%PACKAGE_DIR%\"
copy "start-app.bat" "%PACKAGE_DIR%\"
copy "stop-app.bat" "%PACKAGE_DIR%\"
copy "setup-docker.bat" "%PACKAGE_DIR%\"
copy "DEPLOYMENT_GUIDE.md" "%PACKAGE_DIR%\"
copy "DEPLOYMENT_SUMMARY.md" "%PACKAGE_DIR%\"
copy ".dockerignore" "%PACKAGE_DIR%\"

REM Copy Django application
echo Copying Django application...
xcopy "info" "%PACKAGE_DIR%\info" /E /I /H

REM Create logs directory
mkdir "%PACKAGE_DIR%\logs"

REM Create README for deployment
echo Creating deployment README...
echo InfoTable Application Deployment Package > "%PACKAGE_DIR%\README.txt"
echo. >> "%PACKAGE_DIR%\README.txt"
echo 1. Install Docker Desktop on the target PC >> "%PACKAGE_DIR%\README.txt"
echo 2. Copy this entire folder to the target PC >> "%PACKAGE_DIR%\README.txt"
echo 3. Right-click install-service.bat and "Run as administrator" >> "%PACKAGE_DIR%\README.txt"
echo 4. Access the application at http://localhost or http://YOUR_IP >> "%PACKAGE_DIR%\README.txt"
echo. >> "%PACKAGE_DIR%\README.txt"
echo See DEPLOYMENT_GUIDE.md for detailed instructions. >> "%PACKAGE_DIR%\README.txt"

echo ========================================
echo Deployment package created successfully!
echo ========================================
echo Package location: %PACKAGE_DIR%
echo.
echo Next steps:
echo 1. Copy the '%PACKAGE_NAME%' folder to the target PC
echo 2. Follow the instructions in DEPLOYMENT_GUIDE.md
echo ========================================

pause
