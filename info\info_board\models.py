from django.db import models

class Announcement(models.Model):
    title = models.CharField(max_length=200)
    content = models.TextField()
    date_posted = models.DateTimeField(auto_now_add=True)
    is_active = models.BooleanField(default=True)
    background_image = models.ImageField(upload_to='announcements/', null=True, blank=True)

    def __str__(self):
        return self.title

class EmailMessage(models.Model):
    sender_email = models.EmailField()
    recipient_email = models.EmailField()
    content = models.TextField()